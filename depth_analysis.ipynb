import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import torch
import transformers
from typing import List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (12, 8)

# Load dataset
data_path = "data/cot_collection.jsonl"
print(f"📂 Loading dataset from: {data_path}")

rows = []
with open(data_path, 'r', encoding='utf-8') as f:
    for line in f:
        try:
            rows.append(json.loads(line.strip()))
        except json.JSONDecodeError:
            continue

print(f"📊 Loaded {len(rows)} samples")
print(f"🔍 Sample keys: {list(rows[0].keys()) if rows else 'No data'}")

# Extract prompts and solutions
prompts = [r["user"].strip() for r in rows]
solutions = [r["assistant"].strip() for r in rows]

print(f"📝 Extracted {len(prompts)} prompts and {len(solutions)} solutions")
print(f"\n🔤 Sample prompt (first 200 chars):\n{prompts[0][:200]}...")
print(f"\n🤖 Sample solution (first 200 chars):\n{solutions[0][:200]}...")

# Initialize tokenizer
model_name = "meta-llama/Llama-3.1-8B-Instruct"
print(f"🔤 Loading tokenizer: {model_name}")

tokenizer = transformers.AutoTokenizer.from_pretrained(model_name)
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

print(f"✅ Tokenizer loaded successfully")
print(f"📏 Vocab size: {tokenizer.vocab_size}")

# Helper functions from training script
def find_answer_start(reasoning_ids: torch.Tensor, boxed_token_id: int, answer_fallback: int = 6) -> int:
    """Find the start of the answer section (\\boxed{...})."""
    try:
        boxed_indices = (reasoning_ids == boxed_token_id).nonzero(as_tuple=True)[0]
        if len(boxed_indices) > 0:
            return boxed_indices[0].item()
    except Exception:
        pass
    # Fallback: assume last N tokens are the answer
    return max(0, len(reasoning_ids) - answer_fallback)

# Get boxed token ID
boxed_token_id = None
try:
    boxed_tokens = tokenizer.encode("\\boxed", add_special_tokens=False)
    if boxed_tokens:
        boxed_token_id = boxed_tokens[0]
        print(f"🔍 Found \\boxed token ID: {boxed_token_id}")
    else:
        print("⚠️ Could not find \\boxed token")
except Exception as e:
    print(f"⚠️ Error finding \\boxed token: {e}")

# Analyze token lengths
print("🧮 Analyzing token lengths...")

prompt_lengths = []
solution_lengths = []
reasoning_lengths = []
answer_lengths = []

for i, (prompt, solution) in enumerate(zip(prompts, solutions)):
    if i % 1000 == 0:
        print(f"   Processing sample {i}/{len(prompts)}")

    try:
        # Tokenize prompt and solution
        p_ids = tokenizer(prompt, add_special_tokens=False, return_tensors="pt").input_ids[0]
        s_ids = tokenizer(solution, add_special_tokens=False, return_tensors="pt").input_ids[0]

        prompt_lengths.append(len(p_ids))
        solution_lengths.append(len(s_ids))

        # Find reasoning vs answer split
        if boxed_token_id is not None:
            ans_start = find_answer_start(s_ids, boxed_token_id, 6)
        else:
            # Fallback: assume last 20% is answer
            ans_start = int(len(s_ids) * 0.8)

        reasoning_length = ans_start
        answer_length = len(s_ids) - ans_start

        reasoning_lengths.append(reasoning_length)
        answer_lengths.append(answer_length)

    except Exception as e:
        print(f"⚠️ Error processing sample {i}: {e}")
        continue

print(f"✅ Analyzed {len(prompt_lengths)} samples")

# Create summary statistics
def print_stats(data, name):
    data = np.array(data)
    print(f"\n📊 {name} Statistics:")
    print(f"   Count: {len(data):,}")
    print(f"   Mean: {np.mean(data):.1f}")
    print(f"   Median: {np.median(data):.1f}")
    print(f"   Std: {np.std(data):.1f}")
    print(f"   Min: {np.min(data)}")
    print(f"   Max: {np.max(data)}")
    print(f"   25th percentile: {np.percentile(data, 25):.1f}")
    print(f"   75th percentile: {np.percentile(data, 75):.1f}")
    print(f"   95th percentile: {np.percentile(data, 95):.1f}")
    print(f"   99th percentile: {np.percentile(data, 99):.1f}")
    return data

prompt_stats = print_stats(prompt_lengths, "Prompt Lengths")
solution_stats = print_stats(solution_lengths, "Solution Lengths")
reasoning_stats = print_stats(reasoning_lengths, "Reasoning Lengths")
answer_stats = print_stats(answer_lengths, "Answer Lengths")

# Create comprehensive distribution plots
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
fig.suptitle('Token Length Distributions', fontsize=16, fontweight='bold')

# Helper function for plotting
def plot_distribution(ax, data, title, color, bins=50):
    ax.hist(data, bins=bins, alpha=0.7, color=color, edgecolor='black', linewidth=0.5)
    ax.set_title(title, fontweight='bold')
    ax.set_xlabel('Token Count')
    ax.set_ylabel('Frequency')
    ax.grid(True, alpha=0.3)

    # Add statistics text
    stats_text = f"Mean: {np.mean(data):.1f}\nMedian: {np.median(data):.1f}\nMax: {np.max(data)}"
    ax.text(0.7, 0.9, stats_text, transform=ax.transAxes,
            bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8),
            verticalalignment='top', fontsize=10)

# Plot distributions
plot_distribution(axes[0,0], prompt_lengths, 'Prompt Lengths', 'skyblue')
plot_distribution(axes[0,1], solution_lengths, 'Solution Lengths', 'lightgreen')
plot_distribution(axes[0,2], reasoning_lengths, 'Reasoning Lengths', 'salmon')
plot_distribution(axes[1,0], answer_lengths, 'Answer Lengths', 'gold')

# Box plots for outlier detection
axes[1,1].boxplot([reasoning_lengths], labels=['Reasoning'])
axes[1,1].set_title('Reasoning Length Outliers', fontweight='bold')
axes[1,1].set_ylabel('Token Count')
axes[1,1].grid(True, alpha=0.3)

# Log scale plot for reasoning (to see outliers better)
reasoning_nonzero = [x for x in reasoning_lengths if x > 0]
axes[1,2].hist(reasoning_nonzero, bins=50, alpha=0.7, color='salmon', edgecolor='black', linewidth=0.5)
axes[1,2].set_yscale('log')
axes[1,2].set_title('Reasoning Lengths (Log Scale)', fontweight='bold')
axes[1,2].set_xlabel('Token Count')
axes[1,2].set_ylabel('Frequency (Log Scale)')
axes[1,2].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Identify outliers in reasoning lengths
reasoning_array = np.array(reasoning_lengths)
q75, q25 = np.percentile(reasoning_array, [75, 25])
iqr = q75 - q25
outlier_threshold = q75 + 1.5 * iqr

outliers = reasoning_array[reasoning_array > outlier_threshold]
outlier_indices = np.where(reasoning_array > outlier_threshold)[0]

print(f"🔍 Outlier Analysis for Reasoning Lengths:")
print(f"   Q25: {q25:.1f}")
print(f"   Q75: {q75:.1f}")
print(f"   IQR: {iqr:.1f}")
print(f"   Outlier threshold (Q75 + 1.5*IQR): {outlier_threshold:.1f}")
print(f"   Number of outliers: {len(outliers)} ({len(outliers)/len(reasoning_lengths)*100:.2f}%)")
print(f"   Outlier values: {sorted(outliers)[:10]}{'...' if len(outliers) > 10 else ''}")

if len(outliers) > 0:
    print(f"\n📝 Sample outlier texts:")
    for i, idx in enumerate(outlier_indices[:3]):
        print(f"\n   Outlier {i+1} (index {idx}, {reasoning_lengths[idx]} reasoning tokens):")
        print(f"   Solution: {solutions[idx][:200]}...")

# Analyze different depth configurations
def calculate_depths_and_weights(num_depths=8, use_sentence_depths=False, custom_depths=None):
    """Calculate depths and weights based on configuration."""
    if custom_depths:
        depths = tuple(int(d.strip()) for d in custom_depths.split(','))
        weights = tuple(1.0 for _ in depths)
        return depths, weights

    if use_sentence_depths:
        if num_depths == 4:
            depths = (0, 2, 4, 8)
        else:
            max_sentences = 2 * num_depths
            if num_depths == 1:
                depths = (0,)
            else:
                step = max_sentences // (num_depths - 1)
                depths = tuple([0] + [step * i for i in range(1, num_depths)])
        weights = tuple(1.0 for _ in depths)
        return depths, weights

    # Token-based depths
    max_reasoning_length = max(reasoning_lengths)
    if num_depths == 1:
        depths = (0,)
    else:
        step = max_reasoning_length // (num_depths - 1)
        depths = tuple([0] + [min(step * i, max_reasoning_length) for i in range(1, num_depths)])

    weights = tuple(1.0 for _ in depths)
    return depths, weights

# Test different configurations
configs = [
    (4, False, None, "4 Token-based"),
    (8, False, None, "8 Token-based"),
    (16, False, None, "16 Token-based"),
    (4, True, None, "4 Sentence-based"),
    (8, True, None, "8 Sentence-based"),
]

print("🧮 Depth Configuration Analysis:")
print(f"📊 Max reasoning length in dataset: {max(reasoning_lengths)}")
print(f"📊 Mean reasoning length: {np.mean(reasoning_lengths):.1f}")
print(f"📊 95th percentile reasoning length: {np.percentile(reasoning_lengths, 95):.1f}")

for num_depths, use_sentence, custom, name in configs:
    depths, weights = calculate_depths_and_weights(num_depths, use_sentence, custom)
    print(f"\n   {name}: {depths}")

# Analyze how many samples would benefit from each depth
def analyze_depth_utilization(depths, reasoning_lengths):
    """Analyze what percentage of samples utilize each depth."""
    utilization = []

    for depth in depths:
        if depth == 0:
            # Depth 0 is always utilized (answer only)
            utilized = len(reasoning_lengths)
        else:
            # Count samples with reasoning length >= depth
            utilized = sum(1 for length in reasoning_lengths if length >= depth)

        utilization.append(utilized)

    return utilization

# Test with 8 token-based depths
depths_8, _ = calculate_depths_and_weights(8, False, None)
utilization_8 = analyze_depth_utilization(depths_8, reasoning_lengths)

print(f"\n📊 Depth Utilization Analysis (8 Token-based depths):")
print(f"Depths: {depths_8}")
total_samples = len(reasoning_lengths)

for depth, util in zip(depths_8, utilization_8):
    percentage = util / total_samples * 100
    print(f"   Depth {depth:3d}: {util:5d} samples ({percentage:5.1f}%)")

# Visualize utilization
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# Bar plot of utilization
percentages = [util / total_samples * 100 for util in utilization_8]
bars = ax1.bar(range(len(depths_8)), percentages, color='skyblue', edgecolor='black', alpha=0.7)
ax1.set_xlabel('Depth Index')
ax1.set_ylabel('Utilization (%)')
ax1.set_title('Depth Utilization Percentage', fontweight='bold')
ax1.set_xticks(range(len(depths_8)))
ax1.set_xticklabels([f'D{d}' for d in depths_8], rotation=45)
ax1.grid(True, alpha=0.3)

# Add percentage labels on bars
for bar, pct in zip(bars, percentages):
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
             f'{pct:.1f}%', ha='center', va='bottom', fontsize=9)

# Cumulative reasoning length distribution
sorted_reasoning = np.sort(reasoning_lengths)
cumulative_pct = np.arange(1, len(sorted_reasoning) + 1) / len(sorted_reasoning) * 100

ax2.plot(sorted_reasoning, cumulative_pct, color='red', linewidth=2, label='Cumulative Distribution')
for depth in depths_8[1:]:  # Skip depth 0
    ax2.axvline(x=depth, color='blue', linestyle='--', alpha=0.7, label=f'Depth {depth}' if depth == depths_8[1] else '')

ax2.set_xlabel('Reasoning Length (tokens)')
ax2.set_ylabel('Cumulative Percentage')
ax2.set_title('Reasoning Length Distribution vs Depths', fontweight='bold')
ax2.grid(True, alpha=0.3)
ax2.legend()

plt.tight_layout()
plt.show()

# Import testing functions from depth_demo logic
def token_prefixes(reasoning_ids, depths, boxed_token_id, answer_fallback=6, tokenizer=None):
    """Build Matryoshka prefixes keyed by depth."""
    ans_start = find_answer_start(reasoning_ids, boxed_token_id, answer_fallback)
    answer_ids = reasoning_ids[ans_start:]
    think_ids = reasoning_ids[:ans_start]

    # Encode the answer separator
    if tokenizer is not None:
        answer_sep_ids = tokenizer.encode("\n\nanswer: ", add_special_tokens=False)
        answer_sep_tensor = torch.tensor(answer_sep_ids, dtype=reasoning_ids.dtype, device=reasoning_ids.device)
    else:
        answer_sep_tensor = torch.tensor([], dtype=reasoning_ids.dtype, device=reasoning_ids.device)

    out = {}
    for d in depths:
        if d == 0:
            if tokenizer is not None:
                answer_only_sep = tokenizer.encode("answer: ", add_special_tokens=False)
                answer_only_sep_tensor = torch.tensor(answer_only_sep, dtype=reasoning_ids.dtype, device=reasoning_ids.device)
                out[d] = torch.cat([answer_only_sep_tensor, answer_ids])
            else:
                out[d] = answer_ids
        else:
            out[d] = torch.cat([think_ids[:d], answer_sep_tensor, answer_ids])
    return out

def validate_depth_containment(sample_texts):
    """Validate that smaller depth reasoning is contained in larger depth reasoning."""
    def extract_reasoning_part(text):
        if "\n\nanswer: " in text:
            return text.split("\n\nanswer: ")[0]
        elif "answer: " in text:
            return text.split("answer: ")[0]
        else:
            return text

    sorted_depths = sorted(sample_texts.keys())
    all_passed = True

    for i in range(len(sorted_depths) - 1):
        smaller_depth = sorted_depths[i]
        larger_depth = sorted_depths[i + 1]

        smaller_reasoning = extract_reasoning_part(sample_texts[smaller_depth]).strip()
        larger_reasoning = extract_reasoning_part(sample_texts[larger_depth]).strip()

        if not (smaller_reasoning in larger_reasoning or smaller_reasoning == larger_reasoning):
            all_passed = False
            break

    return all_passed

# Test Matryoshka logic on sample data
def test_matryoshka_depths(num_samples=5, num_depths=8):
    """Test Matryoshka depth logic on sample data."""
    print(f"🧪 Testing Matryoshka Depths (num_depths={num_depths}, samples={num_samples})")

    # Calculate depths
    depths, weights = calculate_depths_and_weights(num_depths, False, None)
    print(f"📊 Using depths: {depths}")

    # Test on random samples
    import random
    test_indices = random.sample(range(len(solutions)), min(num_samples, len(solutions)))

    passed_samples = 0
    total_samples = 0

    for i, idx in enumerate(test_indices):
        print(f"\n{'='*60}")
        print(f"🔬 Testing Sample {i+1} (index {idx})")
        print(f"{'='*60}")

        solution = solutions[idx]
        s_ids = tokenizer(solution, add_special_tokens=False, return_tensors="pt").input_ids[0]

        # Generate texts for all depths
        sample_texts = {}

        for depth in depths:
            if boxed_token_id is not None:
                prefix = token_prefixes(s_ids, (depth,), boxed_token_id, tokenizer=tokenizer)[depth]
                text = tokenizer.decode(prefix, skip_special_tokens=True)
            else:
                # Fallback
                if depth == 0:
                    text = "answer: " + solution[-50:]  # Simple fallback
                else:
                    text = solution[:depth] + "\n\nanswer: " + solution[-50:]

            sample_texts[depth] = text
            print(f"\n🔢 Depth {depth} ({len(tokenizer.encode(text, add_special_tokens=False))} tokens):")
            print(f"{text[:150]}{'...' if len(text) > 150 else ''}")

        # Validate containment
        is_valid = validate_depth_containment(sample_texts)
        total_samples += 1

        if is_valid:
            passed_samples += 1
            print(f"\n✅ VALIDATION PASSED: Depth containment property satisfied")
        else:
            print(f"\n❌ VALIDATION FAILED: Depth containment property violated")

    print(f"\n{'='*60}")
    print(f"🎯 FINAL RESULTS")
    print(f"{'='*60}")
    print(f"✅ Passed: {passed_samples}/{total_samples} samples")
    print(f"📊 Success rate: {passed_samples/total_samples*100:.1f}%" if total_samples > 0 else "📊 No samples tested")

    return passed_samples, total_samples

# Run the test
passed, total = test_matryoshka_depths(num_samples=3, num_depths=4)

# Generate recommendations based on analysis
print("🎯 RECOMMENDATIONS FOR MATRYOSHKA TRAINING")
print("="*60)

max_reasoning = max(reasoning_lengths)
mean_reasoning = np.mean(reasoning_lengths)
p95_reasoning = np.percentile(reasoning_lengths, 95)
p99_reasoning = np.percentile(reasoning_lengths, 99)

print(f"\n📊 Dataset Characteristics:")
print(f"   • Max reasoning length: {max_reasoning} tokens")
print(f"   • Mean reasoning length: {mean_reasoning:.1f} tokens")
print(f"   • 95th percentile: {p95_reasoning:.1f} tokens")
print(f"   • 99th percentile: {p99_reasoning:.1f} tokens")

# Check for extreme outliers
outlier_ratio = len([x for x in reasoning_lengths if x > p95_reasoning]) / len(reasoning_lengths)
extreme_outliers = [x for x in reasoning_lengths if x > p99_reasoning]

print(f"\n⚠️  Outlier Analysis:")
print(f"   • Samples above 95th percentile: {outlier_ratio*100:.2f}%")
print(f"   • Extreme outliers (>99th percentile): {len(extreme_outliers)} samples")

if len(extreme_outliers) > 0:
    print(f"   • Extreme outlier values: {sorted(extreme_outliers)[:5]}{'...' if len(extreme_outliers) > 5 else ''}")

print(f"\n💡 Depth Configuration Recommendations:")

if max_reasoning > p95_reasoning * 3:
    print(f"   ⚠️  WARNING: Extreme outliers detected!")
    print(f"   • Max length ({max_reasoning}) is {max_reasoning/p95_reasoning:.1f}x the 95th percentile")
    print(f"   • Consider using 95th percentile ({p95_reasoning:.0f}) as max depth instead of max length")
    print(f"   • This would prevent most samples from repeating at higher depths")

    # Recommend alternative depth configuration
    alt_depths_8 = tuple([0] + [int(p95_reasoning * i / 7) for i in range(1, 8)])
    print(f"   • Recommended 8 depths (95th percentile): {alt_depths_8}")
else:
    print(f"   ✅ No extreme outliers detected")
    print(f"   • Current max-length-based depths should work well")
    print(f"   • Most samples will utilize different reasoning at each depth")

# Utilization-based recommendations
depths_8, _ = calculate_depths_and_weights(8, False, None)
utilization_8 = analyze_depth_utilization(depths_8, reasoning_lengths)
low_util_depths = [(d, u/len(reasoning_lengths)*100) for d, u in zip(depths_8, utilization_8) if u/len(reasoning_lengths) < 0.1]

if low_util_depths:
    print(f"\n📉 Low Utilization Warning:")
    for depth, util_pct in low_util_depths:
        print(f"   • Depth {depth}: Only {util_pct:.1f}% of samples have enough reasoning")
    print(f"   • Consider reducing number of depths or using percentile-based depths")

print(f"\n🚀 Final Recommendations:")
if max_reasoning > p95_reasoning * 2:
    print(f"   1. Use 95th percentile-based depths to avoid outlier dominance")
    print(f"   2. Consider filtering out extreme outliers (>{p99_reasoning:.0f} tokens)")
    print(f"   3. Test with 4-6 depths initially to ensure good utilization")
else:
    print(f"   1. Current max-length-based approach should work well")
    print(f"   2. 8 depths appear reasonable for this dataset")
    print(f"   3. Monitor training to ensure all depths are being utilized")

print(f"\n✅ Analysis complete! Use the visualizations above to make informed decisions.")
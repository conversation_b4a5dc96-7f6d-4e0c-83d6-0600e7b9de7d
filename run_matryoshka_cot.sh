#!/bin/bash

# Matryoshka Chain-of-Thought Training Script
# This script runs the Matryoshka CoT training with configurable parameters

# Set default values
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
DATA_PATH="data/10k_open_math_reasoning.jsonl"
OUTPUT_DIR="./output/Llama-3.1-8B-Instruct/open_math_reasoning"
NUM_DEPTHS=4
USE_SENTENCE_DEPTHS=false
CUSTOM_DEPTHS=""
BATCH_SIZE=1
LEARNING_RATE=5e-6
NUM_EPOCHS=2
SAVE_STEPS=500
LOGGING_STEPS=100
MAX_LENGTH=2048
DEEPSPEED_CONFIG="deepspeed3.json"
RUN_NAME=""  # Will auto-generate if empty

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --model_name)
            MODEL_NAME="$2"
            shift 2
            ;;
        --data_path)
            DATA_PATH="$2"
            shift 2
            ;;
        --output_dir)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --num_depths)
            NUM_DEPTHS="$2"
            shift 2
            ;;
        --use_sentence_depths)
            USE_SENTENCE_DEPTHS=false
            shift
            ;;
        --custom_depths)
            CUSTOM_DEPTHS="$2"
            shift 2
            ;;
        --batch_size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --learning_rate)
            LEARNING_RATE="$2"
            shift 2
            ;;
        --num_epochs)
            NUM_EPOCHS="$2"
            shift 2
            ;;
        --save_steps)
            SAVE_STEPS="$2"
            shift 2
            ;;
        --logging_steps)
            LOGGING_STEPS="$2"
            shift 2
            ;;
        --max_length)
            MAX_LENGTH="$2"
            shift 2
            ;;
        --deepspeed)
            DEEPSPEED_CONFIG="$2"
            shift 2
            ;;
        --run_name)
            RUN_NAME="$2"
            shift 2
            ;;
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --model_name MODEL          Model name or path (default: $MODEL_NAME)"
            echo "  --data_path PATH            Path to training data (default: $DATA_PATH)"
            echo "  --output_dir DIR            Output directory (default: $OUTPUT_DIR)"
            echo "  --num_depths N              Number of depths (default: $NUM_DEPTHS)"
            echo "  --use_sentence_depths       Use sentence-based depths instead of token-based"
            echo "  --custom_depths DEPTHS      Custom depths (comma-separated, e.g., '0,5,10,20')"
            echo "  --batch_size N              Batch size (default: $BATCH_SIZE)"
            echo "  --learning_rate LR          Learning rate (default: $LEARNING_RATE)"
            echo "  --num_epochs N              Number of epochs (default: $NUM_EPOCHS)"
            echo "  --save_steps N              Save every N steps (default: $SAVE_STEPS)"
            echo "  --logging_steps N           Log every N steps (default: $LOGGING_STEPS)"
            echo "  --max_length N              Maximum sequence length (default: $MAX_LENGTH)"
            echo "  --deepspeed CONFIG          DeepSpeed config file (e.g., deepspeed3.json)"
            echo "  --run_name NAME             WandB run name (default: auto-generated)"
            echo "  -h, --help                  Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done


# Set run name (auto-generate if not provided)
if [ -z "$RUN_NAME" ]; then
    model_name=$(basename "$MODEL_NAME")
    dataset_name=$(basename "$DATA_PATH" .jsonl)
    RUN_NAME="$model_name-$dataset_name"

fi

# Print configuration
echo "🚀 Starting Matryoshka Chain-of-Thought Training"
echo "================================================"
echo "Model: $MODEL_NAME"
echo "Data: $DATA_PATH"
echo "Output: $OUTPUT_DIR"
echo "Depths: $NUM_DEPTHS"
echo "Use sentence depths: $USE_SENTENCE_DEPTHS"
echo "Custom depths: $CUSTOM_DEPTHS"
echo "Batch size: $BATCH_SIZE"
echo "Learning rate: $LEARNING_RATE"
echo "Epochs: $NUM_EPOCHS"
echo "Max length: $MAX_LENGTH"
echo "DeepSpeed config: $DEEPSPEED_CONFIG"
echo "Run name: $RUN_NAME"
echo "================================================"

# Build the command
if [ -n "$DEEPSPEED_CONFIG" ]; then
    CMD="deepspeed train_matryoshka_cot.py"
else
    CMD="python train_matryoshka_cot.py"
fi
CMD="$CMD --model_name_or_path $MODEL_NAME"
CMD="$CMD --data_name $DATA_PATH"
CMD="$CMD --output_dir $OUTPUT_DIR"
CMD="$CMD --num_depths $NUM_DEPTHS"
CMD="$CMD --per_device_train_batch_size $BATCH_SIZE"
CMD="$CMD --learning_rate $LEARNING_RATE"
CMD="$CMD --num_train_epochs $NUM_EPOCHS"
CMD="$CMD --save_steps $SAVE_STEPS"
CMD="$CMD --logging_steps $LOGGING_STEPS"
CMD="$CMD --max_length $MAX_LENGTH"
CMD="$CMD --warmup_steps 100"
CMD="$CMD --weight_decay 0.01"
CMD="$CMD --bf16"
CMD="$CMD --dataloader_drop_last"
CMD="$CMD --remove_unused_columns false"
CMD="$CMD --log_level warning"
CMD="$CMD --run_name $RUN_NAME"

# Add DeepSpeed config if specified
if [ -n "$DEEPSPEED_CONFIG" ]; then
    CMD="$CMD --deepspeed $DEEPSPEED_CONFIG"
fi

# Add optional flags
if [ "$USE_SENTENCE_DEPTHS" = true ]; then
    CMD="$CMD --use_sentence_depths"
fi

if [ -n "$CUSTOM_DEPTHS" ]; then
    CMD="$CMD --custom_depths $CUSTOM_DEPTHS"
fi

# Set logging level to suppress info messages
export TRANSFORMERS_VERBOSITY=warning
export TOKENIZERS_PARALLELISM=false

# Run the training
echo "🔥 Executing: $CMD"
echo ""
eval $CMD

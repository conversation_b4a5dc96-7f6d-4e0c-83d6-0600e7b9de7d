(finetune) junkim100@nlp-server-16:/data_x/junkim100/projects/matryoshka_cot$ bash run_matryoshka_cot.sh > deepspeed.log                                                                                                                                                                                                                                                                                                                                                                                            [1160/1160]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.35s/it]
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
2025-07-09 04:35:18 | WARNING | Gradient accumulation steps mismatch: GradientAccumulationPlugin has 1, DeepSpeed config has 16. Using DeepSpeed's value.
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
wandb: Currently logged in as: junkim100 (junkim) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /data_x/junkim100/projects/matryoshka_cot/wandb/run-20250709_043606-mi3q1t6i
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run Llama-3.1-8B-Instruct-10k_open_math_reasoning
wandb: ⭐️ View project at https://wandb.ai/junkim/matryoshka-cot
wandb: 🚀 View run at https://wandb.ai/junkim/matryoshka-cot/runs/mi3q1t6i
  0%|                                                                                                  | 0/158 [00:00<?, ?it/s][rank3]: Traceback (most recent call last):
[rank3]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 648, in <module>
[rank3]:     train(m_args, d_args, t_args)
[rank3]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 613, in train
[rank3]:     trainer.train()
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
[rank3]:     return inner_training_loop(
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2555, in _inner_training_loop
[rank3]:     tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 3791, in training_step
[rank3]:     self.accelerator.backward(loss, **kwargs)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/accelerator.py", line 2545, in backward
[rank3]:     self.deepspeed_engine_wrapped.backward(loss, sync_gradients=self.sync_gradients, **kwargs)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/utils/deepspeed.py", line 270, in backward
[rank3]:     self.engine.backward(loss, **kwargs)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/utils/nvtx.py", line 20, in wrapped_fn
[rank3]:     ret_val = func(*args, **kwargs)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/runtime/engine.py", line 2216, in backward
[rank3]:     self._do_optimizer_backward(loss, retain_graph)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/runtime/engine.py", line 2162, in _do_optimizer_backward
[rank3]:     self.optimizer.backward(loss, retain_graph=retain_graph)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/utils/nvtx.py", line 20, in wrapped_fn
[rank3]:     ret_val = func(*args, **kwargs)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/runtime/zero/stage3.py", line 2293, in backward
[rank3]:     self.loss_scaler.backward(loss.float(), retain_graph=retain_graph)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/runtime/fp16/loss_scaler.py", line 63, in backward
[rank3]:     scaled_loss.backward(retain_graph=retain_graph)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/_tensor.py", line 648, in backward
[rank3]:     torch.autograd.backward(
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/autograd/__init__.py", line 353, in backward
[rank3]:     _engine_run_backward(
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/autograd/graph.py", line 824, in _engine_run_backward
[rank3]:     return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/autograd/function.py", line 307, in apply
[rank3]:     return user_fn(self, *args)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/amp/autocast_mode.py", line 556, in decorate_bwd
[rank3]:     return bwd(*args, **kwargs)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/runtime/zero/linear.py", line 98, in backward
[rank3]:     grad_output.shape[-1]).t().matmul(input.reshape(-1, input.shape[-1]))
[rank3]: torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 1004.00 MiB. GPU 3 has a total capacity of 47.54 GiB of which 913.69 MiB is free. Process 1562378 has 17.87 GiB memory in use. Including non-PyTorch memory, this process has 28.74 GiB memory in use. Of the allocated memory 27.11 GiB is allocated by PyTorch, and 1.17 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.
 See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
[rank4]:[W709 04:37:31.595717007 TCPStore.cpp:125] [c10d] recvValue failed on SocketImpl(fd=68, addr=[localhost]:56180, remote=[localhost]:29500): Connection reset by peer
Exception raised from recvBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:675 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7b15f7b785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x7b15e0ba8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baafcf (0x7b15e0baafcf in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5bab84a (0x7b15e0bab84a in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x2a9 (0x7b15e0ba52a9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7b15a21e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x7b16962dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x7b1698c94ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x7b1698d26850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank4]:[W709 04:37:31.598785220 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 4] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Connection reset by peer
[rank6]:[W709 04:37:31.595715324 TCPStore.cpp:125] [c10d] recvValue failed on SocketImpl(fd=69, addr=[localhost]:56190, remote=[localhost]:29500): Connection reset by peer
Exception raised from recvBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:675 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x784133d785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x78411cda8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baafcf (0x78411cdaafcf in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5bab84a (0x78411cdab84a in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x2a9 (0x78411cda52a9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7840de3e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x7841d24dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x7841d5094ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x7841d5126850 in /lib/x86_64-linux-gnu/libc.so.6






































(finetune) junkim100@nlp-server-16:/data_x/junkim100/projects/matryoshka_cot$ bash run_matryoshka_cot.sh > deepspeed.log                                                                                                                                                                                                                                                                                                                                                                                            [1160/1160]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.33s/it]
Loading checkpoint shards: 100%|█████████████████████████████████████████████████████████████████| 4/4 [00:29<00:00,  7.35s/it]
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new embeddings will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
The new lm_head weights will be initialized from a multivariate normal distribution that has old embeddings' mean and covariance. As described in this article: https://nlp.stanford.edu/~johnhew/vocab-expansion.html. To disable this, use `mean_resizing=False`
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:11 | WARNING | Loading dataset…
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:14 | WARNING | Prepared 10,000 samples.
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
2025-07-09 04:35:17 | WARNING | Reasoning token analysis: max_length=2027, avg_length=706.9, 95th_percentile=1344
2025-07-09 04:35:17 | WARNING | No extreme outliers detected, using 95th percentile: 1344
2025-07-09 04:35:17 | WARNING | Using token-based depths: (0, 448, 896, 1344)
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
Received unrecognized `WANDB_LOG_MODEL` setting value=matryoshka-run; so disabling `WANDB_LOG_MODEL`
2025-07-09 04:35:18 | WARNING | Gradient accumulation steps mismatch: GradientAccumulationPlugin has 1, DeepSpeed config has 16. Using DeepSpeed's value.
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/distributed/distributed_c10d.py:4631: UserWarning: No device id is provided via `init_process_group` or `barrier `. Using the current device set by the user.
  warnings.warn(  # warn only once
wandb: Currently logged in as: junkim100 (junkim) to https://api.wandb.ai. Use `wandb login --relogin` to force relogin
wandb: Tracking run with wandb version 0.21.0
wandb: Run data is saved locally in /data_x/junkim100/projects/matryoshka_cot/wandb/run-20250709_043606-mi3q1t6i
wandb: Run `wandb offline` to turn off syncing.
wandb: Syncing run Llama-3.1-8B-Instruct-10k_open_math_reasoning
wandb: ⭐️ View project at https://wandb.ai/junkim/matryoshka-cot
wandb: 🚀 View run at https://wandb.ai/junkim/matryoshka-cot/runs/mi3q1t6i
  0%|                                                                                                  | 0/158 [00:00<?, ?it/s][rank3]: Traceback (most recent call last):
[rank3]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 648, in <module>
[rank3]:     train(m_args, d_args, t_args)
[rank3]:   File "/data_x/junkim100/projects/matryoshka_cot/train_matryoshka_cot.py", line 613, in train
[rank3]:     trainer.train()
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2240, in train
[rank3]:     return inner_training_loop(
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 2555, in _inner_training_loop
[rank3]:     tr_loss_step = self.training_step(model, inputs, num_items_in_batch)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 3791, in training_step
[rank3]:     self.accelerator.backward(loss, **kwargs)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/accelerator.py", line 2545, in backward
[rank3]:     self.deepspeed_engine_wrapped.backward(loss, sync_gradients=self.sync_gradients, **kwargs)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/accelerate/utils/deepspeed.py", line 270, in backward
[rank3]:     self.engine.backward(loss, **kwargs)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/utils/nvtx.py", line 20, in wrapped_fn
[rank3]:     ret_val = func(*args, **kwargs)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/runtime/engine.py", line 2216, in backward
[rank3]:     self._do_optimizer_backward(loss, retain_graph)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/runtime/engine.py", line 2162, in _do_optimizer_backward
[rank3]:     self.optimizer.backward(loss, retain_graph=retain_graph)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/utils/nvtx.py", line 20, in wrapped_fn
[rank3]:     ret_val = func(*args, **kwargs)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/runtime/zero/stage3.py", line 2293, in backward
[rank3]:     self.loss_scaler.backward(loss.float(), retain_graph=retain_graph)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/runtime/fp16/loss_scaler.py", line 63, in backward
[rank3]:     scaled_loss.backward(retain_graph=retain_graph)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/_tensor.py", line 648, in backward
[rank3]:     torch.autograd.backward(
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/autograd/__init__.py", line 353, in backward
[rank3]:     _engine_run_backward(
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/autograd/graph.py", line 824, in _engine_run_backward
[rank3]:     return Variable._execution_engine.run_backward(  # Calls into the C++ engine to run the backward pass
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/autograd/function.py", line 307, in apply
[rank3]:     return user_fn(self, *args)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/amp/autocast_mode.py", line 556, in decorate_bwd
[rank3]:     return bwd(*args, **kwargs)
[rank3]:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/deepspeed/runtime/zero/linear.py", line 98, in backward
[rank3]:     grad_output.shape[-1]).t().matmul(input.reshape(-1, input.shape[-1]))
[rank3]: torch.OutOfMemoryError: CUDA out of memory. Tried to allocate 1004.00 MiB. GPU 3 has a total capacity of 47.54 GiB of which 913.69 MiB is free. Process 1562378 has 17.87 GiB memory in use. Including non-PyTorch memory, this process has 28.74 GiB memory in use. Of the allocated memory 27.11 GiB is allocated by PyTorch, and 1.17 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.
 See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
[rank4]:[W709 04:37:31.595717007 TCPStore.cpp:125] [c10d] recvValue failed on SocketImpl(fd=68, addr=[localhost]:56180, remote=[localhost]:29500): Connection reset by peer
Exception raised from recvBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:675 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7b15f7b785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x7b15e0ba8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baafcf (0x7b15e0baafcf in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5bab84a (0x7b15e0bab84a in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x2a9 (0x7b15e0ba52a9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7b15a21e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x7b16962dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x7b1698c94ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x7b1698d26850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank4]:[W709 04:37:31.598785220 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 4] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Connection reset by peer
[rank6]:[W709 04:37:31.595715324 TCPStore.cpp:125] [c10d] recvValue failed on SocketImpl(fd=69, addr=[localhost]:56190, remote=[localhost]:29500): Connection reset by peer
Exception raised from recvBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:675 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x784133d785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x78411cda8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baafcf (0x78411cdaafcf in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5bab84a (0x78411cdab84a in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x2a9 (0x78411cda52a9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7840de3e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x7841d24dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x7841d5094ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x7841d5126850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank5]:[W709 04:37:48.663947668 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 5] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank6]:[W709 04:37:49.662364663 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56190, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x784133d785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x78411cda8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x78411cdaa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x78411cdabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x78411cda5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7840de3e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x7841d24dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x7841d5094ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x7841d5126850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank7]:[W709 04:37:49.662365184 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56152, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7595dbf785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x7595c4fa8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x7595c4faa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x7595c4fabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x7595c4fa5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7595865e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x75967a6dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x75967d294ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x75967d326850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank6]:[W709 04:37:49.665907093 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 6] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank7]:[W709 04:37:49.665991874 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 7] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank6]:[W709 04:37:50.666161122 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56190, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x784133d785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x78411cda8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x78411cdaa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x78411cdabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x78411cda5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7840de3e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x7841d24dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x7841d5094ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x7841d5126850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank6]:[W709 04:37:50.669734982 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 6] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank7]:[W709 04:37:50.666212650 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56152, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7595dbf785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x7595c4fa8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x7595c4faa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x7595c4fabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x7595c4fa5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7595865e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x75967a6dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x75967d294ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x75967d326850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank7]:[W709 04:37:50.670749531 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 7] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank6]:[W709 04:37:51.669942985 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56190, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x784133d785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x78411cda8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x78411cdaa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x78411cdabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x78411cda5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7840de3e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x7841d24dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x7841d5094ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x7841d5126850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank6]:[W709 04:37:51.673231505 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 6] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank7]:[W709 04:37:51.672182400 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56152, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7595dbf785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x7595c4fa8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x7595c4faa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x7595c4fabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x7595c4fa5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7595865e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x75967a6dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x75967d294ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x75967d326850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank7]:[W709 04:37:51.675262847 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 7] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank6]:[W709 04:37:52.673413718 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56190, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x784133d785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x78411cda8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x78411cdaa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x78411cdabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x78411cda5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7840de3e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x7841d24dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x7841d5094ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x7841d5126850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank6]:[W709 04:37:52.676990043 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 6] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank7]:[W709 04:37:52.675422728 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56152, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7595dbf785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x7595c4fa8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x7595c4faa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x7595c4fabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x7595c4fa5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7595865e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x75967a6dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x75967d294ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x75967d326850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank7]:[W709 04:37:52.678775881 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 7] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank7]:[W709 04:37:53.678951882 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56152, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7595dbf785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x7595c4fa8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x7595c4faa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x7595c4fabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x7595c4fa5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7595865e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x75967a6dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x75967d294ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x75967d326850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank7]:[W709 04:37:53.682033081 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 7] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank7]:[W709 04:37:54.682260039 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56152, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7595dbf785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x7595c4fa8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x7595c4faa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x7595c4fabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x7595c4fa5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7595865e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x75967a6dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x75967d294ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x75967d326850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank7]:[W709 04:37:54.685417691 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 7] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank7]:[W709 04:37:55.685598563 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56152, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7595dbf785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x7595c4fa8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x7595c4faa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x7595c4fabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x7595c4fa5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7595865e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x75967a6dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x75967d294ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x75967d326850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank7]:[W709 04:37:55.689138058 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 7] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank7]:[W709 04:37:56.689376497 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56152, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7595dbf785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x7595c4fa8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x7595c4faa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x7595c4fabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x7595c4fa5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7595865e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x75967a6dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x75967d294ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x75967d326850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank7]:[W709 04:37:56.692887579 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 7] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
[rank7]:[W709 04:37:57.693087115 TCPStore.cpp:106] [c10d] sendBytes failed on SocketImpl(fd=69, addr=[localhost]:56152, remote=[localhost]:29500): Broken pipe
Exception raised from sendBytes at /pytorch/torch/csrc/distributed/c10d/Utils.hpp:653 (most recent call first):
frame #0: c10::Error::Error(c10::SourceLocation, std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >) + 0x98 (0x7595dbf785e8 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libc10.so)
frame #1: <unknown function> + 0x5ba8bfe (0x7595c4fa8bfe in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #2: <unknown function> + 0x5baa458 (0x7595c4faa458 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #3: <unknown function> + 0x5babc3e (0x7595c4fabc3e in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #4: c10d::TCPStore::check(std::vector<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> >, std::allocator<std::__cxx11::basic_string<char, std::char_traits<char>, std::allocator<char> > > > const&) + 0x298 (0x7595c4fa5298 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cpu.so)
frame #5: c10d::ProcessGroupNCCL::heartbeatMonitor() + 0x379 (0x7595865e09f9 in /mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/lib/libtorch_cuda.so)
frame #6: <unknown function> + 0xdbbf4 (0x75967a6dbbf4 in /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/../lib/libstdc++.so.6)
frame #7: <unknown function> + 0x94ac3 (0x75967d294ac3 in /lib/x86_64-linux-gnu/libc.so.6)
frame #8: <unknown function> + 0x126850 (0x75967d326850 in /lib/x86_64-linux-gnu/libc.so.6)

[rank7]:[W709 04:37:57.696438684 ProcessGroupNCCL.cpp:1662] [PG ID 0 PG GUID 0(default_pg) Rank 7] Failed to check the "should dump" flag on TCPStore, (maybe TCPStore server has shut down too early), with error: Broken pipe
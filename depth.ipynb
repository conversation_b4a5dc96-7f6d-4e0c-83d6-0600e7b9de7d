# token_prefixes.py (or inline above the trainer)
def token_prefixes(reasoning_ids, depths, answer_len=8):
    """
    Build Matryoshka prefixes by *token length* instead of sentence count.

    reasoning_ids : 1-D tensor of all tokens in the assistant solution
    depths        : iterable of integers (e.g. 0, 16, 32…)
    answer_len    : number of tokens at the tail that constitute the answer
    returns       : dict {depth: tensor_of_tokens}
    """
    answer_ids = reasoning_ids[-answer_len:]      # keep answer intact
    think_ids  = reasoning_ids[:-answer_len]      # pure reasoning
    out = {}
    for d in depths:
        if d == 0:
            out[d] = answer_ids
        else:
            out[d] = torch.cat([think_ids[:d], answer_ids])
    return out

def compute_loss(self, model, inputs, return_outputs=False, num_items_in_batch=None):
    """
    Matryoshka-CoT loss with length-based prefixes.
    Depths are read from self.depths (e.g. [0,16,32,64]).
    """
    prompt_ids     = inputs["prompt_ids"]          # (B, P)
    solutions      = inputs["solution_txt"]        # list[str]
    device         = prompt_ids.device
    B              = prompt_ids.size(0)

    total_loss = 0.0
    for depth, w in zip(self.depths, self.weights.to(device)):
        seqs, labels = [], []

        for i in range(B):
            # 1) Tokenise the full assistant solution once
            full_ids = self.tokenizer(
                solutions[i],
                add_special_tokens=False,
                return_tensors="pt"
            ).input_ids[0].to(device)

            # 2) Slice it according to the current depth
            tgt_ids = token_prefixes(full_ids, (depth,))[depth]

            # 3) Build full training sequence  [prompt  ⊕  truncated-assistant]
            seq = torch.cat([prompt_ids[i], tgt_ids])
            lab = seq.clone()
            lab[: len(prompt_ids[i])] = IGNORE_INDEX   # mask the prompt

            seqs.append(seq)
            labels.append(lab)

        # 4) Pad to equal length inside the current depth micro-batch
        seqs = torch.nn.utils.rnn.pad_sequence(
            seqs, batch_first=True, padding_value=self.tokenizer.pad_token_id
        )
        labels = torch.nn.utils.rnn.pad_sequence(
            labels, batch_first=True, padding_value=IGNORE_INDEX
        )
        attn = seqs.ne(self.tokenizer.pad_token_id)

        # 5) Forward pass & accumulate weighted loss
        outputs    = model(input_ids=seqs, attention_mask=attn, labels=labels)
        total_loss = total_loss + w * outputs.loss

    total_loss = total_loss / self.weights.sum()   # normalise
    return (total_loss, None) if return_outputs else total_loss

import json, random, re, pathlib, itertools
from typing import List, Dict, Tuple

import pandas as pd
pd.set_option("display.max_colwidth", None)

# Simple sentence splitter: ".", "?", "!"  followed by whitespace
_SENT_SPLIT = re.compile(r"(?<=[.!?])\s+")
THINK_LEADS = re.compile(
    r'^(?:Hmm|Wait|So|Therefore|Thus|Hence|First|Next|Finally)[,.\s]+',
    flags=re.IGNORECASE,
)

def split_steps(text: str) -> List[str]:
    return [s.strip() for s in _SENT_SPLIT.split(text) if s.strip()]

def depth_prefixes(reasoning: str,
                   depths: Tuple[int, ...] = (0, 2, 4, 8)
                  ) -> Dict[int, str]:
    """
    depth 0 -> answer-only (last sentence)
    depth d -> first d sentences + same final answer sentence
    """
    steps = split_steps(reasoning)
    out = {}
    for d in depths:
        if d == 0:
            out[d] = steps[-1]
        else:
            body = " ".join(steps[:d])
            out[d] = (body + " " + steps[-1]).strip()
    return out


# ↗️  config
# JSONL_PATH = "/data_x/junkim100/projects/matryoshka_cot/data/10k_cot_collection.jsonl"
JSONL_PATH = "/data_x/junkim100/projects/matryoshka_cot/data/10k_open_math_reasoning.jsonl"
N_SAMPLES  = 3           # how many random rows to inspect
DEPTHS     = (0, 2, 4, 8)

# --- read jsonl (stream-friendly) ---
def read_jsonl(path):
    with open(path, "r", encoding="utf-8") as f:
        for line in f:
            yield json.loads(line)

all_rows = list(read_jsonl(JSONL_PATH))
print(f"Total rows: {len(all_rows):,}")

samples = random.sample(all_rows, k=N_SAMPLES)


records = []
for idx, row in enumerate(samples, 1):
    user_prompt = row["user"].strip()
    assistant_raw = THINK_LEADS.sub("", row["assistant"].strip())  # strip lead tokens

    splits = depth_prefixes(assistant_raw, DEPTHS)
    rec = {"sample": idx, "user": user_prompt, "full_answer": assistant_raw}
    for d in DEPTHS:
        rec[f"depth_{d}"] = splits[d]
    records.append(rec)

df = pd.DataFrame(records)
df


def display_sample(row):
    print("=" * 100)
    print(f"USER:\n{row.user}\n")
    for d in DEPTHS:
        col = f"depth_{d}"
        print(f"[depth {d}]".ljust(12, " "), row[col], "\n")

_ = df.apply(display_sample, axis=1)


import matplotlib.pyplot as plt

lengths = [len(split_steps(THINK_LEADS.sub("", r["assistant"]))) for r in all_rows]
plt.figure(figsize=(6,3))
plt.hist(lengths, bins=40, color="#1f77b4")
plt.xlabel("sentences per assistant rationale")
plt.ylabel("#examples")
plt.title("Sentence count distribution")
plt.show()

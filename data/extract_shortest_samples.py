#!/usr/bin/env python3
"""
Extract the 10k shortest samples from datasets based on character length.
Saves to data/ folder, shuffles output, and reports token statistics for min/max samples only.
"""

import json
import random
import statistics
from pathlib import Path
from typing import List, Tuple, Dict
from transformers import AutoTokenizer


def load_tokenizer():
    """Load the Llama 3.1 8B Instruct tokenizer."""
    print("🔧 Loading Llama 3.1 8B Instruct tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained("meta-llama/Llama-3.1-8B-Instruct")
    return tokenizer


def calculate_char_length(sample: dict) -> int:
    """Calculate the total character length of user + assistant content."""
    user_text = sample.get("user", "")
    assistant_text = sample.get("assistant", "")
    return len(user_text) + len(assistant_text)


def calculate_token_lengths(sample: dict, tokenizer) -> Tuple[int, int, int]:
    """Calculate token lengths for user, assistant, and total content."""
    user_text = sample.get("user", "")
    assistant_text = sample.get("assistant", "")

    user_tokens = len(tokenizer.encode(user_text, add_special_tokens=False))
    assistant_tokens = len(tokenizer.encode(assistant_text, add_special_tokens=False))
    total_tokens = user_tokens + assistant_tokens

    return user_tokens, assistant_tokens, total_tokens


def print_token_statistics_for_extremes(samples: List[dict], tokenizer):
    """Print token statistics for shortest and longest samples only."""
    if len(samples) < 2:
        print("   Not enough samples for statistics")
        return

    print("📊 Token statistics for extreme samples:")

    # Tokenize shortest sample
    shortest_sample = samples[0]
    shortest_user, shortest_assistant, shortest_total = calculate_token_lengths(
        shortest_sample, tokenizer
    )

    # Tokenize longest sample
    longest_sample = samples[-1]
    longest_user, longest_assistant, longest_total = calculate_token_lengths(
        longest_sample, tokenizer
    )

    # Calculate median from a small sample in the middle
    mid_idx = len(samples) // 2
    sample_indices = [0, mid_idx - 1, mid_idx, mid_idx + 1, len(samples) - 1]
    sample_indices = list(set([i for i in sample_indices if 0 <= i < len(samples)]))

    user_tokens = []
    assistant_tokens = []
    total_tokens = []

    for idx in sample_indices:
        u, a, t = calculate_token_lengths(samples[idx], tokenizer)
        user_tokens.append(u)
        assistant_tokens.append(a)
        total_tokens.append(t)

    print(f"   User tokens:")
    print(f"     Shortest: {shortest_user:,} tokens")
    print(f"     Longest:  {longest_user:,} tokens")
    print(f"     Median:   {statistics.median(user_tokens):,} tokens")

    print(f"   Assistant tokens:")
    print(f"     Shortest: {shortest_assistant:,} tokens")
    print(f"     Longest:  {longest_assistant:,} tokens")
    print(f"     Median:   {statistics.median(assistant_tokens):,} tokens")

    print(f"   Total tokens:")
    print(f"     Shortest: {shortest_total:,} tokens")
    print(f"     Longest:  {longest_total:,} tokens")
    print(f"     Median:   {statistics.median(total_tokens):,} tokens")


def extract_shortest_samples(
    input_file: str, output_file: str, num_samples: int = 10000
):
    """
    Extract the shortest samples from a JSONL file based on character length.

    Args:
        input_file: Path to input JSONL file
        output_file: Output filename (will be saved in data/ folder)
        num_samples: Number of shortest samples to extract
    """
    print(f"📖 Reading samples from {input_file}...")

    # Read all samples with their character lengths
    samples_with_lengths: List[Tuple[int, dict]] = []  # (char_length, sample)

    with open(input_file, "r", encoding="utf-8") as f:
        for line_num, line in enumerate(f, 1):
            if line_num % 100000 == 0:
                print(f"   Processed {line_num:,} lines...")

            try:
                sample = json.loads(line.strip())
                char_length = calculate_char_length(sample)
                samples_with_lengths.append((char_length, sample))
            except json.JSONDecodeError as e:
                print(f"   Warning: Skipping invalid JSON at line {line_num}: {e}")
                continue
            except Exception as e:
                print(f"   Warning: Error processing line {line_num}: {e}")
                continue

    print(f"✅ Read {len(samples_with_lengths):,} valid samples")

    # Sort by character length (shortest first)
    print(f"🔄 Sorting samples by character length...")
    samples_with_lengths.sort(key=lambda x: x[0])

    # Extract the shortest samples
    shortest_samples_with_lengths = samples_with_lengths[:num_samples]
    shortest_samples = [sample for _, sample in shortest_samples_with_lengths]

    # Print character length statistics
    char_lengths = [length for length, _ in shortest_samples_with_lengths]
    print(f"📊 Character length statistics for {num_samples} shortest samples:")
    print(f"   Shortest: {min(char_lengths):,} characters")
    print(f"   Longest:  {max(char_lengths):,} characters")
    print(f"   Median:   {statistics.median(char_lengths):,} characters")

    # Load tokenizer and print token statistics for extremes only
    tokenizer = load_tokenizer()
    print_token_statistics_for_extremes(shortest_samples, tokenizer)

    # Shuffle the samples to randomize order
    print(f"🔀 Shuffling samples...")
    random.shuffle(shortest_samples)

    # Write to output file in current directory
    output_path = Path(output_file)
    print(f"💾 Writing {num_samples} shortest samples to {output_path}...")
    with open(output_path, "w", encoding="utf-8") as f:
        for sample in shortest_samples:
            f.write(json.dumps(sample, ensure_ascii=False) + "\n")

    print(f"✅ Successfully created {output_path}")


def main():
    """Main function to process datasets."""
    # Set random seed for reproducibility
    random.seed(42)

    # Process both datasets
    datasets = [
        ("open_math_reasoning.jsonl", "10k_open_math_reasoning.jsonl"),
        ("cot_collection.jsonl", "10k_cot_collection.jsonl"),
    ]

    for input_file, output_file in datasets:
        input_path = Path(input_file)
        if not input_path.exists():
            print(f"❌ Warning: {input_file} does not exist, skipping...")
            continue

        print(f"\n{'='*60}")
        print(f"Processing {input_file}")
        print(f"{'='*60}")

        try:
            extract_shortest_samples(input_file, output_file, 10000)
        except Exception as e:
            print(f"❌ Error processing {input_file}: {e}")
            continue

    print(f"\n🎉 All done! Check the data/ folder for the output files.")


if __name__ == "__main__":
    main()

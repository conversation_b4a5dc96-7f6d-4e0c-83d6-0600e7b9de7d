# save as build_cot_dataset.py
from datasets import load_dataset
import json, re, sys

THINK_REGEX = re.compile(r'<think>.*?</think>', re.DOTALL | re.IGNORECASE)
LEAD_INS = re.compile(
    r'^(?:Hmm|Wait|So|Therefore|Thus|Hence|First|Next|Finally)[,.\s]+',
    re.IGNORECASE
)

def strip_think(text: str) -> str:
    text = THINK_REGEX.sub(' ', text)
    text = LEAD_INS.sub('', text)
    return re.sub(r'\s+', ' ', text).strip()

def extract(dataset_name, split, user_key, assist_key):
    ds = load_dataset(dataset_name, split=split, trust_remote_code=True)
    for row in ds:
        user = row.get(user_key, '').strip()
        assistant = strip_think(row.get(assist_key, '').strip())
        if user and assistant:
            yield {"user": user, "assistant": assistant}

def cot_collection(out_path="cot_collection.jsonl"):
    writers = [
        extract("kaist-ai/CoT-Collection", "train", "source", "rationale"),
        # extract("nvidia/OpenMathReasoning", "cot", "problem", "generated_solution"),
    ]
    with open(out_path, "w", encoding="utf8") as f:
        for gen in writers:
            for sample in gen:
                f.write(json.dumps(sample, ensure_ascii=False) + "\n")

def open_math_reasoning(out_path="open_math_reasoning.jsonl"):
    writers = [
        extract("nvidia/OpenMathReasoning", "cot", "problem", "generated_solution"),
    ]
    with open(out_path, "w", encoding="utf8") as f:
        for gen in writers:
            for sample in gen:
                f.write(json.dumps(sample, ensure_ascii=False) + "\n")

if __name__ == "__main__":
    cot_collection("cot_collection.jsonl")
    open_math_reasoning("open_math_reasoning.jsonl")

#!/usr/bin/env python3
"""
Extract the 10k shortest samples from datasets based on total content length.
"""

import json
import argparse
from pathlib import Path
from typing import List, <PERSON><PERSON>


def calculate_sample_length(sample: dict) -> int:
    """Calculate the total character length of user + assistant content."""
    user_text = sample.get("user", "")
    assistant_text = sample.get("assistant", "")
    return len(user_text) + len(assistant_text)


def extract_shortest_samples(input_file: str, output_file: str, num_samples: int = 10000):
    """
    Extract the shortest samples from a JSONL file.
    
    Args:
        input_file: Path to input JSONL file
        output_file: Path to output JSONL file
        num_samples: Number of shortest samples to extract
    """
    print(f"📖 Reading samples from {input_file}...")
    
    # Read all samples with their lengths
    samples_with_lengths: List[Tuple[int, dict]] = []
    
    with open(input_file, 'r', encoding='utf-8') as f:
        for line_num, line in enumerate(f, 1):
            if line_num % 100000 == 0:
                print(f"   Processed {line_num:,} lines...")
            
            try:
                sample = json.loads(line.strip())
                length = calculate_sample_length(sample)
                samples_with_lengths.append((length, sample))
            except json.JSONDecodeError as e:
                print(f"   Warning: Skipping invalid JSON at line {line_num}: {e}")
                continue
    
    print(f"✅ Read {len(samples_with_lengths):,} valid samples")
    
    # Sort by length (shortest first)
    print(f"🔄 Sorting samples by length...")
    samples_with_lengths.sort(key=lambda x: x[0])
    
    # Extract the shortest samples
    shortest_samples = samples_with_lengths[:num_samples]
    
    print(f"📊 Length statistics for {num_samples} shortest samples:")
    lengths = [length for length, _ in shortest_samples]
    print(f"   Min length: {min(lengths):,} characters")
    print(f"   Max length: {max(lengths):,} characters")
    print(f"   Avg length: {sum(lengths) / len(lengths):.1f} characters")
    
    # Write to output file
    print(f"💾 Writing {num_samples} shortest samples to {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        for _, sample in shortest_samples:
            f.write(json.dumps(sample, ensure_ascii=False) + '\n')
    
    print(f"✅ Successfully created {output_file}")


def main():
    parser = argparse.ArgumentParser(description="Extract shortest samples from JSONL datasets")
    parser.add_argument("input_file", help="Input JSONL file path")
    parser.add_argument("output_file", help="Output JSONL file path")
    parser.add_argument("--num_samples", type=int, default=10000, 
                       help="Number of shortest samples to extract (default: 10000)")
    
    args = parser.parse_args()
    
    # Validate input file exists
    if not Path(args.input_file).exists():
        print(f"❌ Error: Input file '{args.input_file}' does not exist")
        return 1
    
    # Create output directory if needed
    output_path = Path(args.output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    try:
        extract_shortest_samples(args.input_file, args.output_file, args.num_samples)
        return 0
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
